# YOLOv10 目标检测Demo

这是一个基于YOLOv10的目标检测演示应用，支持本地权重文件和可视化界面操作。

## 🚀 快速开始

### 1. 环境准备

确保您已经激活了虚拟环境：
```bash
cd /Users/<USER>/Documents/GordonChatSpace/yolo10-test
source yolov10_env/bin/activate
cd yolov10
```

### 2. 启动Demo应用

```bash
streamlit run demo_app.py
```

应用将在浏览器中自动打开，默认地址：http://localhost:8501

## 📁 项目结构

```
yolov10/
├── weights/                    # 本地权重文件目录
│   ├── yolov10n.pt            # 最快模型 (10.9 MB)
│   ├── yolov10s.pt            # 平衡模型 (31.4 MB)
│   ├── yolov10m.pt            # 中等模型 (63.8 MB)
│   ├── yolov10b.pt            # 较好模型 (79.1 MB)
│   ├── yolov10l.pt            # 大型模型 (99.6 MB)
│   └── yolov10x.pt            # 最佳模型 (122.3 MB)
├── demo_app.py                 # Streamlit Demo应用
├── download_weights.py         # 权重下载脚本
├── test_local_weights.py       # 本地权重测试脚本
└── README_DEMO.md             # 本说明文档
```

## 🎯 功能特性

### 主要功能
- ✅ **本地权重加载**: 使用本地.pt文件，加载速度更快
- ✅ **多模型支持**: 6种不同大小的YOLOv10模型
- ✅ **实时检测**: 上传图片即可进行目标检测
- ✅ **参数调节**: 可调节置信度阈值和图像尺寸
- ✅ **结果可视化**: 显示检测框和置信度
- ✅ **详细统计**: 显示检测到的目标类别和数量

### 支持的模型
| 模型 | 大小 | 特点 | 推荐用途 |
|------|------|------|----------|
| YOLOv10n | 10.9 MB | 最快，精度较低 | 实时应用 |
| YOLOv10s | 31.4 MB | 平衡速度和精度 | 通用推荐 |
| YOLOv10m | 63.8 MB | 中等精度 | 平衡应用 |
| YOLOv10b | 79.1 MB | 较好精度 | 高质量检测 |
| YOLOv10l | 99.6 MB | 大型模型 | 高精度需求 |
| YOLOv10x | 122.3 MB | 最佳精度，较慢 | 最高质量 |

## 🔧 使用方法

### 1. 选择模型
在左侧边栏选择合适的YOLOv10模型：
- 对于快速测试，推荐使用 **YOLOv10s**
- 对于高精度检测，推荐使用 **YOLOv10l** 或 **YOLOv10x**

### 2. 调整参数
- **置信度阈值**: 控制检测的敏感度 (0.0-1.0)
  - 较低值：检测更多目标，可能包含误检
  - 较高值：只检测高置信度目标，可能遗漏一些目标
- **图像尺寸**: 影响检测精度和速度 (320-1280)
  - 较小尺寸：速度快，精度较低
  - 较大尺寸：精度高，速度较慢

### 3. 上传图片
- 支持格式：PNG, JPG, JPEG
- 建议大小：不超过10MB
- 点击"开始检测"进行分析

### 4. 查看结果
- 左侧显示原始图片
- 右侧显示检测结果（带标注框）
- 下方显示检测统计和详细信息

## 📋 支持的目标类别

本模型基于COCO数据集训练，支持检测80种常见目标，包括：
- **人物**: 人
- **交通工具**: 汽车、自行车、摩托车、公交车、火车、卡车、船、飞机
- **动物**: 猫、狗、马、牛、羊、鸟、大象、熊、斑马、长颈鹿
- **日常用品**: 手机、笔记本电脑、书、椅子、桌子、瓶子、杯子等
- **食物**: 苹果、香蕉、披萨、蛋糕、三明治等

## 🛠️ 工具脚本

### 下载权重文件
```bash
python download_weights.py
```
- 可选择下载单个或所有模型
- 自动显示下载进度
- 支持断点续传

### 测试本地权重
```bash
python test_local_weights.py
```
- 检查本地权重文件完整性
- 测试模型加载和预测功能
- 验证环境配置是否正确

## ⚡ 性能优化建议

1. **模型选择**:
   - 开发测试：使用YOLOv10n或YOLOv10s
   - 生产环境：根据精度要求选择合适模型

2. **参数调优**:
   - 图像尺寸：640是很好的平衡点
   - 置信度：0.25是推荐的默认值

3. **硬件要求**:
   - CPU：支持所有模型
   - GPU：可显著提升大模型推理速度

## 🐛 常见问题

### Q: 模型加载很慢怎么办？
A: 确保使用本地权重文件，首次导入ultralytics可能较慢，后续会更快。

### Q: 检测结果不准确怎么办？
A: 尝试调整置信度阈值或使用更大的模型（如YOLOv10l/x）。

### Q: 应用启动失败怎么办？
A: 检查虚拟环境是否正确激活，确保所有依赖已安装。

## 📞 技术支持

如果遇到问题，请检查：
1. Python虚拟环境是否正确激活
2. 所有依赖包是否正确安装
3. 权重文件是否完整下载
4. 网络连接是否正常

---

**享受使用YOLOv10进行目标检测！** 🎉

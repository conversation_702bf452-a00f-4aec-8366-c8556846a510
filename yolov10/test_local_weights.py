#!/usr/bin/env python3
"""
测试本地YOLOv10权重文件
"""

import os
import sys
import time
from pathlib import Path
import numpy as np
from PIL import Image

def test_local_weights():
    """测试本地权重文件"""
    print("测试本地YOLOv10权重文件")
    print("=" * 50)
    
    # 检查weights目录
    weights_dir = Path("weights")
    if not weights_dir.exists():
        print("❌ weights目录不存在")
        return False
    
    # 检查可用的权重文件
    available_models = []
    for model_name in ['yolov10n', 'yolov10s', 'yolov10m', 'yolov10b', 'yolov10l', 'yolov10x']:
        weight_path = weights_dir / f"{model_name}.pt"
        if weight_path.exists():
            size_mb = weight_path.stat().st_size / (1024 * 1024)
            available_models.append((model_name, weight_path, size_mb))
            print(f"✓ 发现权重文件: {model_name}.pt ({size_mb:.1f} MB)")
    
    if not available_models:
        print("❌ 未发现任何权重文件")
        return False
    
    print(f"\n总共发现 {len(available_models)} 个权重文件")
    
    # 测试导入
    print("\n测试导入ultralytics...")
    try:
        from ultralytics import YOLOv10
        print("✓ ultralytics导入成功")
    except Exception as e:
        print(f"❌ ultralytics导入失败: {e}")
        return False
    
    # 测试最小的模型 (通常是yolov10n)
    test_model = min(available_models, key=lambda x: x[2])  # 选择最小的文件
    model_name, weight_path, size_mb = test_model
    
    print(f"\n测试模型: {model_name} ({size_mb:.1f} MB)")
    
    try:
        # 加载模型
        print("正在加载模型...")
        start_time = time.time()
        model = YOLOv10(str(weight_path))
        load_time = time.time() - start_time
        print(f"✓ 模型加载成功 (耗时: {load_time:.2f}秒)")
        
        # 创建测试图像
        print("创建测试图像...")
        test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        test_image_pil = Image.fromarray(test_image)
        
        # 进行预测
        print("正在进行预测...")
        start_time = time.time()
        results = model.predict(
            source=test_image_pil,
            imgsz=640,
            conf=0.25,
            save=False,
            verbose=False
        )
        predict_time = time.time() - start_time
        
        print(f"✓ 预测完成 (耗时: {predict_time:.2f}秒)")
        print(f"✓ 检测到 {len(results[0].boxes)} 个目标")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    success = test_local_weights()
    
    if success:
        print("\n" + "=" * 50)
        print("🎉 本地权重测试通过！")
        print("\n现在您可以运行以下命令启动Demo应用:")
        print("streamlit run demo_app.py")
        print("\nDemo应用将自动使用本地权重文件，加载速度更快！")
    else:
        print("\n" + "=" * 50)
        print("❌ 本地权重测试失败")
        print("\n请检查:")
        print("1. 是否已下载权重文件到weights目录")
        print("2. 是否已正确安装ultralytics包")
        print("3. Python环境是否正确激活")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

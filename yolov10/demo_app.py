import streamlit as st
import cv2
import numpy as np
from PIL import Image
import tempfile
import os
from ultralytics import YOLOv10

# 设置页面配置
st.set_page_config(
    page_title="YOLOv10 目标检测Demo",
    page_icon="🎯",
    layout="wide"
)

# 缓存模型加载
@st.cache_resource
def load_model(model_name):
    """加载YOLOv10模型"""
    try:
        # 使用本地权重文件
        weights_path = f"weights/{model_name}.pt"
        if os.path.exists(weights_path):
            model = YOLOv10(weights_path)
            st.success(f"✓ 成功加载本地模型: {weights_path}")
        else:
            # 如果本地文件不存在，尝试从网络加载
            st.warning(f"本地权重文件不存在: {weights_path}，尝试从网络加载...")
            model = YOLOv10.from_pretrained(f'jameslahm/{model_name}')
            st.info("✓ 从网络加载模型成功")
        return model
    except Exception as e:
        st.error(f"模型加载失败: {e}")
        return None

def check_local_weights():
    """检查本地权重文件"""
    weights_dir = "weights"
    if not os.path.exists(weights_dir):
        return {}

    available_weights = {}
    for model_name in ['yolov10n', 'yolov10s', 'yolov10m', 'yolov10b', 'yolov10l', 'yolov10x']:
        weight_path = os.path.join(weights_dir, f"{model_name}.pt")
        if os.path.exists(weight_path):
            size_mb = os.path.getsize(weight_path) / (1024 * 1024)
            available_weights[model_name] = size_mb

    return available_weights

def main():
    st.title("🎯 YOLOv10 目标检测Demo")
    st.markdown("---")

    # 检查本地权重
    local_weights = check_local_weights()

    # 侧边栏配置
    st.sidebar.header("模型配置")

    # 显示本地权重状态
    if local_weights:
        st.sidebar.success(f"✓ 发现 {len(local_weights)} 个本地权重文件")
        with st.sidebar.expander("本地权重详情"):
            for model, size in local_weights.items():
                st.write(f"• {model}: {size:.1f} MB")
    else:
        st.sidebar.warning("⚠️ 未发现本地权重文件")
        st.sidebar.info("将从网络下载模型（较慢）")

    # 模型选择
    model_options = {
        "YOLOv10n (最快)": "yolov10n",
        "YOLOv10s (平衡)": "yolov10s",
        "YOLOv10m (中等)": "yolov10m",
        "YOLOv10b (较好)": "yolov10b",
        "YOLOv10l (大型)": "yolov10l",
        "YOLOv10x (最佳)": "yolov10x"
    }

    selected_model = st.sidebar.selectbox(
        "选择模型",
        options=list(model_options.keys()),
        index=1  # 默认选择YOLOv10s
    )

    model_name = model_options[selected_model]

    # 检测参数
    confidence_threshold = st.sidebar.slider(
        "置信度阈值",
        min_value=0.0,
        max_value=1.0,
        value=0.25,
        step=0.05
    )

    image_size = st.sidebar.slider(
        "图像尺寸",
        min_value=320,
        max_value=1280,
        value=640,
        step=32
    )

    # 主界面
    col1, col2 = st.columns([1, 1])

    with col1:
        st.header("📤 上传图片")
        uploaded_file = st.file_uploader(
            "选择图片文件",
            type=['png', 'jpg', 'jpeg'],
            help="支持PNG、JPG、JPEG格式"
        )

        if uploaded_file is not None:
            # 显示原始图片
            image = Image.open(uploaded_file)
            st.image(image, caption="原始图片", use_column_width=True)

            # 检测按钮
            if st.button("🔍 开始检测", type="primary"):
                with st.spinner("正在加载模型..."):
                    model = load_model(model_name)

                if model is not None:
                    with st.spinner("正在进行目标检测..."):
                        try:
                            # 进行预测
                            results = model.predict(
                                source=image,
                                imgsz=image_size,
                                conf=confidence_threshold,
                                save=False
                            )

                            # 获取带标注的图片
                            annotated_image = results[0].plot()

                            # 转换颜色空间 (BGR to RGB)
                            annotated_image_rgb = cv2.cvtColor(annotated_image, cv2.COLOR_BGR2RGB)

                            # 在右侧显示结果
                            with col2:
                                st.header("🎯 检测结果")
                                st.image(annotated_image_rgb, caption="检测结果", use_column_width=True)

                                # 显示检测统计
                                if len(results[0].boxes) > 0:
                                    st.success(f"检测到 {len(results[0].boxes)} 个目标")

                                    # 显示检测详情
                                    st.subheader("检测详情")
                                    for i, box in enumerate(results[0].boxes):
                                        conf = float(box.conf[0])
                                        cls = int(box.cls[0])
                                        class_name = results[0].names[cls]
                                        st.write(f"目标 {i+1}: {class_name} (置信度: {conf:.2f})")
                                else:
                                    st.warning("未检测到任何目标")

                        except Exception as e:
                            st.error(f"检测过程中出现错误: {e}")

    with col2:
        if uploaded_file is None:
            st.header("🎯 检测结果")
            st.info("请先上传图片进行检测")

    # 使用说明和提示
    st.markdown("---")
    st.header("📖 使用说明")

    col1, col2, col3 = st.columns(3)

    with col1:
        st.info("""
        **步骤 1: 选择模型**
        - YOLOv10n: 最快，精度较低
        - YOLOv10s: 平衡速度和精度
        - YOLOv10m: 中等精度
        - YOLOv10l/x: 最高精度，较慢
        """)

    with col2:
        st.info("""
        **步骤 2: 调整参数**
        - 置信度阈值: 控制检测敏感度
        - 图像尺寸: 影响检测精度和速度
        - 较大尺寸 = 更高精度，更慢速度
        """)

    with col3:
        st.info("""
        **步骤 3: 上传图片**
        - 支持 PNG, JPG, JPEG 格式
        - 建议图片大小不超过 10MB
        - 点击"开始检测"进行分析
        """)

    # 性能提示
    if local_weights:
        st.success("🚀 使用本地权重文件，检测速度更快！")
    else:
        st.warning("⚠️ 首次使用需要下载模型，请耐心等待...")

    # 支持的目标类别
    st.markdown("---")
    with st.expander("📋 支持检测的目标类别 (COCO数据集)"):
        categories = [
            "人", "自行车", "汽车", "摩托车", "飞机", "公交车", "火车", "卡车", "船", "交通灯",
            "消防栓", "停车标志", "停车计时器", "长椅", "鸟", "猫", "狗", "马", "羊", "牛",
            "大象", "熊", "斑马", "长颈鹿", "背包", "雨伞", "手提包", "领带", "手提箱", "飞盘",
            "滑雪板", "单板滑雪", "运动球", "风筝", "棒球棒", "棒球手套", "滑板", "冲浪板", "网球拍", "瓶子",
            "酒杯", "杯子", "叉子", "刀", "勺子", "碗", "香蕉", "苹果", "三明治", "橙子",
            "西兰花", "胡萝卜", "热狗", "披萨", "甜甜圈", "蛋糕", "椅子", "沙发", "盆栽植物", "床",
            "餐桌", "厕所", "电视", "笔记本电脑", "鼠标", "遥控器", "键盘", "手机", "微波炉", "烤箱",
            "烤面包机", "水槽", "冰箱", "书", "时钟", "花瓶", "剪刀", "泰迪熊", "吹风机", "牙刷"
        ]

        # 分列显示类别
        cols = st.columns(4)
        for i, category in enumerate(categories):
            with cols[i % 4]:
                st.write(f"• {category}")

    # 技术信息
    st.markdown("---")
    with st.expander("🔧 技术信息"):
        st.markdown("""
        **YOLOv10 特点:**
        - 实时端到端目标检测
        - 无需非极大值抑制(NMS)后处理
        - 更高的检测精度和更快的推理速度
        - 支持多种模型尺寸以适应不同需求

        **本Demo特性:**
        - 本地权重文件加载，提升加载速度
        - 实时检测结果显示
        - 可调节检测参数
        - 支持多种图片格式
        """)

if __name__ == "__main__":
    main()

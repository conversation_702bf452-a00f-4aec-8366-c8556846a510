#!/usr/bin/env python3
"""
简单的YOLOv10测试脚本
"""

import sys
import os

def test_imports():
    """测试导入"""
    print("测试导入...")
    try:
        import torch
        print(f"✓ PyTorch版本: {torch.__version__}")
        
        import cv2
        print(f"✓ OpenCV版本: {cv2.__version__}")
        
        from ultralytics import YOLOv10
        print("✓ YOLOv10导入成功")
        
        return True
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_model_loading():
    """测试模型加载"""
    print("\n测试模型加载...")
    try:
        from ultralytics import YOLOv10
        
        # 尝试加载最小的模型
        print("正在加载YOLOv10n模型...")
        model = YOLOv10.from_pretrained('jameslahm/yolov10n')
        print("✓ 模型加载成功")
        
        return model
    except Exception as e:
        print(f"✗ 模型加载失败: {e}")
        return None

def test_prediction(model):
    """测试预测功能"""
    print("\n测试预测功能...")
    try:
        import numpy as np
        from PIL import Image
        
        # 创建一个测试图像
        test_image = np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8)
        test_image_pil = Image.fromarray(test_image)
        
        print("正在进行预测...")
        results = model.predict(source=test_image_pil, imgsz=640, conf=0.25, save=False)
        
        print(f"✓ 预测成功，检测到 {len(results[0].boxes)} 个目标")
        return True
        
    except Exception as e:
        print(f"✗ 预测失败: {e}")
        return False

def main():
    print("YOLOv10 环境测试")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        print("\n❌ 导入测试失败，请检查环境配置")
        return False
    
    # 测试模型加载
    model = test_model_loading()
    if model is None:
        print("\n❌ 模型加载失败")
        return False
    
    # 测试预测
    if not test_prediction(model):
        print("\n❌ 预测测试失败")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 所有测试通过！YOLOv10环境配置正确")
    print("\n现在您可以运行以下命令启动Demo应用:")
    print("streamlit run demo_app.py")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

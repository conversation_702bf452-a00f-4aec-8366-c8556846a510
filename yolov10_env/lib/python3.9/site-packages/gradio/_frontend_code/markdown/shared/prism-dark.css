.dark .md .token.comment,
.dark .md .token.prolog,
.dark .md .token.doctype,
.dark .md .token.cdata {
	color: hsl(30, 20%, 50%);
}

.dark .md .token.punctuation {
	opacity: 0.7;
}

.dark .md .token.namespace {
	opacity: 0.7;
}

.dark .md .token.property,
.dark .md .token.tag,
.dark .md .token.boolean,
.dark .md .token.number,
.dark .md .token.constant,
.dark .md .token.symbol {
	color: hsl(350, 40%, 70%);
}

.dark .md .token.selector,
.dark .md .token.attr-name,
.dark .md .token.string,
.dark .md .token.char,
.dark .md .token.builtin,
.dark .md .token.inserted {
	color: hsl(75, 70%, 60%);
}

.dark .md .token.operator,
.dark .md .token.entity,
.dark .md .token.url,
.dark .md .language-css .token.string,
.dark .md .style .token.string,
.dark .md .token.variable {
	color: hsl(40, 90%, 60%);
}

.dark .md .token.atrule,
.dark .md .token.attr-value,
.dark .md .token.keyword {
	color: hsl(350, 40%, 70%);
}

.dark .md .token.regex,
.dark .md .token.important {
	color: #e90;
}

.dark .md .token.important,
.dark .md .token.bold {
	font-weight: bold;
}
.dark .md .token.italic {
	font-style: italic;
}

.dark .md .token.entity {
	cursor: help;
}

.dark .md .token.deleted {
	color: red;
}
